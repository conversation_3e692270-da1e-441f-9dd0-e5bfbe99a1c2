# -*- coding: utf-8 -*-
"""
    BluePrint: uc, User Center, 用户中心（对应'我的账户'栏目）

"""

import os
import random
import urllib

import json
import datetime
from datetime import datetime, timedelta
import pymysql
import settings

from flask import Blueprint, request, session, g, redirect, url_for, abort, \
    render_template, flash, current_app, jsonify, send_from_directory, make_response
from api import pysql
import utils

from . import models
import re
uc = Blueprint('uc', __name__)




@uc.route('/software_management')
def software_management_list():
    cur = pysql.CURSOR()
    if 'logged_user_id' not in session:
        return redirect(url_for('login', url=request.url))
    try:
        # 先判断包含的类型，info和nodes中类型不一定对应
        # nodes决定层级，info可能有全局情况

        software_type_nodes=models.get_software_type_from_nodes()

        software_type=software_type_nodes

        utils.logger.debug("让我看看0：\n%s" % software_type_nodes)

        data={}
        sql="""select count(`version`) as value ,`type` as `name` from rms_software_info where action='正常' and status='使用中' group by `type`"""
        vdata=cur.select_all(sql)
        if not vdata:
            vdata = []  # 确保vdata始终是一个列表，即使数据库为空
        return render_template('uc/software_management.html',data=data,software_type=software_type,vdata=vdata)
    except Exception as e:
        return error_page(e)

# 通过用户选择【类型】，获取【名称】
@uc.route('/get_software_types',methods=['GET'])
def get_software_types():
    software_types=models.get_software_type_from_nodes()
    return jsonify(software_types)

@uc.route('/get_software_names',methods=['GET'])
def get_software_names():
    choose_software_type="'"+request.args.get('type')+"'"
    software_name=models.get_software_name_from_info(choose_software_type)
    return jsonify(software_name)

# 通过用户选择【类型】、【名称】，获取【版本】
@uc.route('/get_software_versions',methods=['GET'])
def get_software_versions():
    choose_software_type="'"+request.args.get('type')+"'"
    choose_software_name="'"+request.args.get('name')+"'"
    software_version=models.get_software_version_from_info(type=choose_software_type,name=choose_software_name)
    return jsonify(software_version)

# 获取全部组件信息
@uc.route('/get_all_software_info',methods=['GET'])
def get_all_software_info():
    software_version=models.get_software_info()
    return jsonify(software_version)




#TODO:查询函数逻辑
@uc.route('/software_check_start',methods=['POST','GET'])
def software_check_start():

    data = request.data
    data=data = json.loads(data)

    #需要找的id_list
    find_list=[]

    # 查找每个row对应的编号，item对应data=[{},{},{}]中{}
    for item in data:
        #每次loop应该重置
        software_type=''
        software_name=''
        software_version=''
        #先判断全不是 全部 的情况==skip
        if item['type']=='全部' and item['name']=='全部' and item['version']=='全部':
            utils.logger.debug("全不是全部")
            cur = pysql.CURSOR()
            sql_check = "SELECT * FROM rms_software_rule where status='使用中' and action='正常' order by id DESC"
            # 执行查询
            result_list=cur.select_all(sql_check)

            new_list=[]
            rule_elements = []
            for rules in result_list:
                    # 获取 rule 字段并去除首尾的 @ 符号
                    rule_parts = rules['rule'].strip('@').split(',')

                    # 去除每个部分两端的 @ 符号并将它们转换为整数
                    rule_numbers = [int(part.strip('@')) for part in rule_parts]
                    utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

                    new_dict={}
                    for num in rule_numbers:
                        # @17@ (str) -> 17 (int)
                        utils.logger.debug("num值:%s" % num)
                        num=int(str(num).replace('@',''))
                        info=models.get_info('*','id',num)
                        info=info[0]

                        # 添加到新dict（拼出展示的字符）

                        type_value = info['type']
                        name_value = info['name']
                        version_value = info['version']
                        new_dict.update({'id':rules['id'],type_value: str(name_value) +' '+ str(version_value)})
                        utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
                    new_list.append(new_dict)
                    utils.logger.debug("new_list数据结构:\n%s" %new_list)
            return jsonify(new_list)

        else:

            # type没有全部的可能，type为全部则name\ version也是全部
            software_type="'"+item['type']+"'"

            if item['name']!='全部':
                software_name="'"+item['name']+"'"
            if item['name']=='全部':
                #name为全部则version也是全部
                utils.logger.debug("怎么错了2？\n%s"% item)
                name_list=models.get_info('`id`','`type`',"'"+item['type']+"'")

                utils.logger.debug("name_list\n%s"% name_list)

                if not find_list:
                    find_list =[[sub_item['id']] for sub_item in name_list]
                else:

                    utils.logger.debug("find_list\n%s\n"% find_list)

                    find_list = [sublist + [sub_item['id']] for sublist in find_list for sub_item in name_list]

            utils.logger.debug("怎么错了？\n%s"% item)
            if item['version']!='全部':
                software_version="'"+item['version']+"'"
            if item['version']=='全部':
                if item['name']=='全部':
                    #无事发生，因为'name'添加过这种可能
                    find_list=find_list
                else:
                    version_list=models.get_info('`id`','`name`',"'"+item['name']+"'")
                    utils.logger.debug("version_list\n%s"% version_list)
                    if not find_list:
                        find_list =[[sub_item['id']] for sub_item in version_list]
                    else:
                        find_list = [sublist + [sub_item['id']] for sublist in find_list for sub_item in version_list]

            if software_type!='' and software_name!='' and software_version!='':
                cur = pysql.CURSOR()
                sql="select `id` from rms_software_info \
                    WHERE `type`=%s \
                    AND `name`=%s \
                    AND `version`=%s \
                    AND (action='正常' and status='使用中')" % (software_type,software_name,software_version)
                ans=cur.select_all(sql)
                utils.logger.debug("三非无\n%s"% ans)

                if find_list!=[]:
                    utils.logger.debug("三非无find_list\n%s"% find_list)
                    for i in find_list:
                        i.extend([d['id'] for d in ans])

                else:
                    find_list.append([d['id'] for d in ans])

    #查询list，去rms_software_rule查找对应的表
    #find_list = list(set(find_list))
    utils.logger.debug("用户需要查询的find_list:\n%s" % find_list)

    # 生成 SQL 查询
    conditions = []
    for sub_find_list in find_list:
        condition = " AND ".join(["rule LIKE '%@" + str(item) + "@%'" for item in sub_find_list])
        conditions.append("(" + condition + ")")

    cur = pysql.CURSOR()
    if conditions:
        sql_check = '''
            SELECT * FROM rms_software_rule
            WHERE (status = '使用中' and action = '正常')
            AND ({})
        '''.format(" OR ".join(conditions))
    else:
        sql_check = '''
            SELECT * FROM rms_software_rule
            WHERE (status = '使用中' and action = '正常')
        '''
    utils.logger.debug("sql模糊查询语句：\n%s"%sql_check)
    # 执行查询
    result_list=cur.select_all(sql_check)
    # result_list 如下[{u'id': 1, u'rule': u'@1@,@4@,@7@,@14@'}]
    utils.logger.debug("sql查询结果:\n%s" % result_list)

    new_list=[]
    rule_elements = []

    # 检查是否有查询结果
    if not result_list:
        # 返回空结果标识，前端将显示"无匹配内容"而不是alert
        return jsonify({"empty_result": True, "message": "无匹配内容", "data": []})

    for rules in result_list:
        # 获取 rule 字段并去除首尾的 @ 符号
        rule_parts = rules['rule'].strip('@').split(',')

        # 去除每个部分两端的 @ 符号并将它们转换为整数
        rule_numbers = [int(part.strip('@')) for part in rule_parts]
        utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

        new_dict={}
        for num in rule_numbers:
            # @17@ (str) -> 17 (int)
            utils.logger.debug("num值:%s" % num)
            num=int(str(num).replace('@',''))
            info=models.get_info('*','id',num)
            if not info:
                continue  # 跳过不存在的组件
            info=info[0]

            # 添加到新dict（拼出展示的字符）

            type_value = info['type']
            name_value = info['name']
            version_value = info['version']
            new_dict.update({'id':rules['id'],type_value: str(name_value) +' '+ str(version_value)})
            utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
        if new_dict:  # 只有当字典不为空时才添加
            new_list.append(new_dict)
        utils.logger.debug("new_list数据结构:\n%s" %new_list)

    # 如果处理后的列表为空，返回空结果标识
    if not new_list:
        return jsonify({"empty_result": True, "message": "无组合内容", "data": []})

    return jsonify(new_list)

@uc.route('/software_rule_delete',methods=['POST'])
def software_rule_delete():
    try:
        data = request.data

        data=data = json.loads(data)

        utils.logger.debug("delete999:\n%s" % data)
        # 按loop删除
        cur = pysql.CURSOR()
        import time
        id=int(time.time()*1000)
        for row in data:
            sql="select * from rms_software_rule where `id`=%s" % row['id']
            result = cur.select_all(sql)
            if not result:
                continue  # 跳过不存在的规则
            ans = result[0]
            rule_content = ans['rule']

            insert_data={
                'user':session['logged_user_id'],
                'action_id':id,
                'rule':rule_content,
                'action':"删除",
                "type":"删除规则",
                "time":utils.now(),
                "rule_id":row['id']
            }
            cur = pysql.CURSOR()
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)
            sql="""UPDATE rms_software_rule  SET & WHERE @"""
            data={"action":"注销"}
            where={"id":row['id']}
            cur.update(sql,data,where)
            check_multiple_rule()



        return jsonify("success")
    except Exception as e:
        return error_page(e)



@uc.route('/software_rule_outline',methods=['POST'])
def software_rule_outline():
    try:
        data = request.data

        data=data = json.loads(data)

        utils.logger.debug("outline999:\n%s" % data)
        # 按loop删除
        cur = pysql.CURSOR()
        import time
        id=int(time.time()*1000)
        for row in data:
            sql="select * from rms_software_rule where `id`=%s" % row['id']
            result = cur.select_all(sql)
            if not result:
                continue  # 跳过不存在的规则
            ans = result[0]
            rule_content = ans['rule']

            insert_data={
                'user':session['logged_user_id'],
                'action_id':id,
                'rule':rule_content,
                'action':"注销",
                "type":"注销规则",
                "time":utils.now(),
                "rule_id":row['id']
            }
            cur = pysql.CURSOR()
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)
            sql="""UPDATE rms_software_rule  SET & WHERE @"""
            data={"status":"注销"}
            where={"id":row['id']}
            cur.update(sql,data,where)
            check_multiple_rule()



        return jsonify("success")
    except Exception as e:
        return error_page(e)

@uc.route('/software_type_add',methods=['POST'])
def software_type_add():
    try:
        data = request.data
        data=data = json.loads(data)
        utils.logger.debug('添加了啥\n%s'% data)
        ans_num=0
        for row in data:
            # 先检测库里有没有模块
            cur = pysql.CURSOR()
            sql="select count(*) as c from rms_software_info \
                WHERE `type`=%s \
                AND `name`=%s \
                AND `version`=%s \
                AND (action='正常' and status='使用中')" % ("'"+row['type']+"'","'"+row['name']+"'","'"+row['version']+"'")
            ans= int(cur.select_all(sql)[0]['c'])
            utils.logger.debug('当前库存在？\n%s' % ans)
            ans_num+=ans

        # 库中有对应信息
        if ans_num!=0:
            software_type_nodes=models.get_software_type_from_nodes()
            software_type=software_type_nodes
            return jsonify({"status": "fail", "message": "库中存在模块","software_type":software_type})
        # 库中无信息
        else:
            for row in data:
                insert_data={
                    'type':row['type'],
                    'name':row['name'],
                    'version':row['version']
                }
                cur = pysql.CURSOR()
                sql="INSERT INTO rms_software_info (&) VALUES (@)"
                cur.insert(sql,insert_data)

                software_type_nodes=models.get_software_type_from_nodes()
                software_type=software_type_nodes
        return jsonify({"status": "success", "message": "提交成功","software_type":software_type})
    except Exception as e:
        return error_page(e)



@uc.route('/software_rule_add',methods=['POST'])
def software_rule_add():
    try:
        utils.logger.debug("HELLO?")
        data = request.data

        data=data = json.loads(data)
        utils.logger.debug("\nadd_rule传参:\n%s\n"%data)

        add_rule_list=[]
        seen=[]

        # 传参格式为[{},{},{},...,{}]
        # {'type':,'name':,'version':[,,,]}

        #预检查，判断合不合法，把包含'version':['全部',,,,]--改为---'version':['全部']
        for item in data:
            if(item['type']+" "+item['name']) in seen:
                utils.logger.debug("\nseen:\n%s\n"% seen)
                return jsonify({"status": "fail", "message": "添加了相同的模块与类型！"})
            seen.append(item['type']+" "+item['name'])

            for i in item['version']:
                if i =='全部':
                    item['version']=['全部']
                    break
        utils.logger.debug("预检查结束!: seen:\n%s"% seen)
        #构建[[],[],[]]，后续需要对[]笛卡尔乘积
        cur = pysql.CURSOR()
        for item in data:
            same_list=[]
            for ver in item['version']:
                if ver!='全部':
                    sql="select * from rms_software_info \
                        WHERE `type`=%s \
                        AND `name`=%s \
                        AND `version`=%s \
                        AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'","'"+ver+"'")
                    result = cur.select_all(sql)
                    if not result:
                        continue  # 跳过不存在的版本
                    same_list.append(result[0]['id'])
                elif ver=='全部':
                    sql="select * from rms_software_info \
                        WHERE `type`=%s \
                        AND `name`=%s \
                        AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'")
                    ans=cur.select_all(sql)
                    if not ans:
                        continue  # 跳过没有记录的项
                    for i in ans:
                        same_list.append(i['id'])

            if same_list:  # 只有当有数据时才添加到规则列表
                add_rule_list.append(same_list)

        utils.logger.debug("\nadd_rule_list（笛卡尔乘积之前）:\n%s\n"%add_rule_list)

        # 检查是否有规则可以添加
        if not add_rule_list:
            return jsonify({"status": "fail", "message": "没有可添加的规则或指定的组件不存在"})

        #笛卡尔乘积 + "@"
        import itertools
        output_list = [["@{}@".format(x) for x in combination] for combination in itertools.product(*add_rule_list)]
        utils.logger.debug("\noutput_list（add_rule笛卡尔乘积之后）:\n%s\n"%output_list)

        #插表（增加规则）
        for sub in output_list:
            sub_rule=[]
            sub_rule = ",".join(sub)
            insert_data={
                'rule':sub_rule
            }
            cur = pysql.CURSOR()
            sql="INSERT INTO rms_software_rule (&) VALUES (@)"
            last_id=cur.insert(sql,insert_data)
            utils.logger.debug("新插入的规则ID:\n%s" % last_id)

            import time
            id=int(time.time()*1000)
            log_insert_data={
                'user':session['logged_user_id'],
                'action_id':id,
                'rule':sub_rule,
                'action':"增加",
                "type":"增加规则",
                "time":utils.now(),
                "rule_id":last_id

            }
            cur = pysql.CURSOR()
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,log_insert_data)

        # 检查和清理子集规则
        check_multiple_rule()

        sql= "select * from rms_software_log where action_id='%s'" % str(id)
        result_list=cur.select_all(sql)
        new_list=[]
        rule_elements = []
        for rules in result_list:
                # 获取 rule 字段并去除首尾的 @ 符号
                rule_parts = rules['rule'].strip('@').split(',')

                # 去除每个部分两端的 @ 符号并将它们转换为整数
                rule_numbers = [int(part.strip('@')) for part in rule_parts]
                utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

                new_dict={}
                for num in rule_numbers:
                    # @17@ (str) -> 17 (int)
                    utils.logger.debug("num值:%s" % num)
                    num=int(str(num).replace('@',''))
                    info=models.get_info('*','id',num)
                    if not info:
                        continue  # 跳过不存在的组件
                    info=info[0]

                    # 添加到新dict（拼出展示的字符）

                    type_value = info['type']
                    name_value = info['name']
                    version_value = info['version']
                    new_dict.update({'id':rules['id'],type_value: str(name_value) +' '+ str(version_value)})
                    utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
                new_list.append(new_dict)
                utils.logger.debug("ADD中new_list数据结构:\n%s" %new_list)


        return jsonify({"status": "success", "message": "提交成功","data":new_list})

    except Exception as e:
        return jsonify({"status": "fail", "message": "存在或包含信息"})

@uc.route('/software_add_check',methods=['POST'])
def software_add_check():
    data = request.data
    data = json.loads(data)
    utils.logger.debug("\nadd_CHECK传参:\n%s\n"%data)

    cur = pysql.CURSOR()
    # 判断这次增加一共多少个
    if not data or len(data) == 0:
        return jsonify({"status": "error", "message": "没有需要提交的数据"})

    first_check = data[0]['id']
    # 获取action_id
    sql = "select * from rms_software_log where id=%s" % first_check
    action_log = cur.select_all(sql)

    if not action_log:
        return jsonify({"status": "error", "message": "无效的记录ID"})

    action_id = action_log[0]['action_id']
    srv_type = action_log[0]['type']

    # 获取全部action
    sql = "select * from rms_software_log where action_id='%s'" % str(action_id)
    ans_list = cur.select_all(sql)

    full_id_list = []
    for item in ans_list:
        full_id_list.append(item['id'])

    real_id = [int(item['id']) for item in data]
    missing_values = [item for item in full_id_list if item not in real_id]
    utils.logger.debug('\n【real_id】变量的值为:\n%s' % real_id)

    utils.logger.debug('\n【missing_values】变量的值为:\n%s' % missing_values)
    if missing_values:
        sql = "DELETE FROM rms_software_log WHERE id IN ({});".format(",".join(str(value) for value in missing_values))
        cur.delete(sql)

    if srv_type == '修改规则':
        utils.logger.debug('--------修改规则---------')
        # 修改类删除json内容
        json_data = ans_list[0]['json_data']
        utils.logger.debug('\n【json_data】变量的值为:\n%s' % json_data)
        json_data = json.loads(json_data)
        for i in json_data:
            utils.logger.debug('\n【i】变量的值为:\n%s' % i)
            sql = "DELETE FROM rms_software_rule WHERE (status='使用中' and action='正常' and rule='%s')" % i
            cur.delete(sql)

    success_count = 0
    for item in data:
        sql = "select * from rms_software_log where id=%s" % int(item['id'])
        real_ans = cur.select_all(sql)
        utils.logger.debug('\n【real_ans】变量的值为:\n%s' % real_ans)
        for i in real_ans:
            insert_data = {
                'rule': i['rule']
            }
            sql = "INSERT INTO rms_software_rule (&) VALUES (@)"
            result = cur.insert(sql, insert_data)
            if result:
                success_count += 1

    # 检查和清理子集规则
    deleted_rules = check_multiple_rule()
    message = f"提交成功，已处理{success_count}条规则"
    if deleted_rules > 0:
        message += f"，清理了{deleted_rules}条冗余子集规则"

    return jsonify({"status": "success", "message": message})

@uc.route('/software_add_cancel',methods=['POST'])
def software_add_cancel():
    data = request.data
    data=data = json.loads(data)
    utils.logger.debug("\nadd_cancel传参:\n%s\n"%data)
    action_id=data[0]['action_id']
    cur = pysql.CURSOR()
    sql="delete from rms_software_log where action_id='%s' " % action_id
    cur.delete(sql)
    return jsonify({"status": "success", "message": "提交成功"})


@uc.route('/software_rule_change',methods=['POST'])
def software_rule_change():
    try:
        import itertools
        # 这里格式：
        # {
        #   "rowsToChange:[{只需要获取'id'},{},{}],  id 是规则id
        #   "modifyData":[{'name':,'type':,'version':,'relation':},{},{}]"
        # }
        data = request.data

        data=data = json.loads(data)
        utils.logger.debug("混合data是啥:\n%s" % data)

        change_list_id=data.get('rowsToChange',[])
        utils.logger.debug("用户需要修改规则的list:\n%s" % change_list_id)

        modify_list=data.get('modifyData',[])
        utils.logger.debug("用户想要修改、增加、修改的的list:\n%s" % modify_list)


        #预检查，判断合不合法，把包含'version':['全部',,,,]--改为---'version':['全部']

        seen=[]
        utils.logger.debug("预检查开始")
        for item in modify_list:
            for i in item['version']:
                utils.logger.debug(i)
                if i =='全部':
                    item['version']=['全部']
        for item in modify_list:
            for i in item['version']:
                if(item['type']+" "+item['name']+" "+i+" "+item['relation']) in seen:
                    utils.logger.debug("预检查报错!: seen:\n%s"% seen)
                    return jsonify({"status": "fail", "message": "添加了相同的模块！"})
            seen.append(item['type']+" "+item['name']+" "+i+" "+item['relation'])
        utils.logger.debug("预检查结束!: seen:\n%s"% seen)

        ### 处理modifyData
        # 先判断relation（删除，增加，更改 关联）
        # 先增加后删除
        change_add=[]
        change_switch=[]
        final_delete=[]

        cur = pysql.CURSOR()
        # 增加关联
        for item in modify_list:
            if item['relation']=='增加关联':
                change_add_before=[]
                for ver in item['version']:

                    if ver !='全部':
                        sql="select * from rms_software_info \
                                WHERE `type`=%s \
                                AND `name`=%s \
                                AND `version`=%s \
                                AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'","'"+ver+"'")
                        ans=cur.select_all(sql)[0]['id']
                        change_add_before.append(ans)
                    elif ver =='全部':
                        sql="select * from rms_software_info \
                            WHERE `type`=%s \
                            AND `name`=%s \
                            AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'")
                        ans=cur.select_all(sql)
                        for i in ans:
                            change_add_before.append(i['id'])
                change_add.append(change_add_before)

        # 修改关联
        for item in modify_list:
            if item['relation']=='修改关联':
                change_switch_before=[]
                for ver in item['version']:
                    if ver !='全部':
                        sql="select * from rms_software_info \
                                WHERE `type`=%s \
                                AND `name`=%s \
                                AND `version`=%s \
                                AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'","'"+ver+"'")
                        ans=cur.select_all(sql)[0]['id']
                        change_switch_before.append(ans)
                    elif ver =='全部':
                        sql="select * from rms_software_info \
                            WHERE `type`=%s \
                            AND `name`=%s \
                            AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'")
                        ans=cur.select_all(sql)
                        for i in ans:
                            change_switch_before.append(i['id'])
                change_switch.append(change_switch_before)
        utils.logger.debug("结束修改关联\n%s\n" % change_switch )



        # 删除关联
        for item in modify_list:
            if item['relation']=='删除关联':
                change_delete=[]
                for ver in item['version']:
                    if ver !='全部':
                        sql="select * from rms_software_info \
                                WHERE `type`=%s \
                                AND `name`=%s \
                                AND `version`=%s \
                                AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'","'"+ver+"'")
                        ans=cur.select_all(sql)[0]['id']
                        change_delete.append(ans)
                    elif ver =='全部':
                        sql="select * from rms_software_info \
                                WHERE `type`=%s \
                                AND `name`=%s \
                                AND (action='正常' and status='使用中')" % ("'"+item['type']+"'","'"+item['name']+"'")
                        ans=cur.select_all(sql)
                        for i in ans:
                            change_delete.append(i['id'])
                final_delete.extend(change_delete)

        # 去重并保留顺序
        result = []
        delete_seen = set()
        for item in final_delete:
            if item not in delete_seen:
                result.append(item)
                delete_seen.add(item)

        final_delete=result
        utils.logger.debug("\n需要增加的（笛卡尔之前）：\n%s\n需要删除的:\n%s\n需要修改的:\n%s\n"%(change_add,final_delete,change_switch))
        #     需要增加的（笛卡尔之前）：
        #       [[14, 15], [10, 11]]
        #     需要删除的:
        #        [16, 17, 18, 19, 20]

        # 这块删除同时增加删除的情况
        # change_add_delete= [[x for x in sublist if x not in final_delete] for sublist in change_add]

        #duplicate_value为上面[[],[],[]]的展平->[,,,]
        duplicate_value_add=[item for sublist in change_add for item in sublist]
        change_add_delete=change_add

        #笛卡尔
        change_add_delete_after=[list(combination) for combination in itertools.product(*change_add_delete)]
        change_add= [list(dict.fromkeys(sublist)) for sublist in change_add_delete_after]

        # #duplicate_value为上面[[],[],[]]的展平->[,,,]
        # duplicate_value_switch=[item for sublist in change_switch for item in sublist]
        # change_switch_delete=change_switch

        # #笛卡尔
        # change_switch_delete_after=[list(combination) for combination in itertools.product(*change_switch_delete)]
        # change_switch= [list(dict.fromkeys(sublist)) for sublist in change_switch_delete_after]


        ### 处理rowsToChange
        change_list=[]
        change_list_origin=[]
        cur = pysql.CURSOR()
        for row in change_list_id:
            sql="select * from rms_software_rule WHERE (status='使用中' and action='正常' and id=%s)" % row['id']
            rule_by_id=cur.select_all(sql)[0]['rule']
            utils.logger.debug("处理rowsToChange怎么回事？:\n%s\n" % rule_by_id)
            change_list_origin.append(rule_by_id)
            # 对rule_by_id去除@@
            result = [int(x[1:-1]) for x in rule_by_id.split(',') if x.strip().startswith('@') and x.strip().endswith('@')]

            #change_list去重（增加类[1,2,3]%%[[3,4],[5,6]] ->[1,2]%%[[3,4],[5,6]]）
            # result=[item for item in result if item not in duplicate_value_add]

            utils.logger.debug("rule规则:\n%s\n" % result)
            change_list.append(result)

        utils.logger.debug("全部rule规则集合:\n%s\n" % change_list)


        utils.logger.debug("检查change_add:\n%s\n" % change_add)

        utils.logger.debug("检查change_switch:\n%s\n" % change_switch)

        #处理修改关联
        include_list=[]
        if change_switch:
            utils.logger.debug("修改关联的方法!")
            for sub_switch in change_switch:
                ans=models.get_info("*","id",sub_switch[0])
                sql="select * from rms_software_info \
                    WHERE `type`=%s AND `name`=%s \
                    AND (action='正常' and status='使用中')"  % ("'"+ans[0]['type']+"'","'"+ans[0]['name']+"'")
                new_ans=cur.select_all(sql)
                for i in new_ans:
                    include_list.append(i['id'])
                utils.logger.debug("需要从change_list删除的（sub）\n%s\n" % include_list)

                # chenge_list 减去include_list这部分内容
                # [[1,2,3],[1,3,5],[1,3,6]]%%[3,5] ---> [[1,2],[1],[1,6]]
                change_list=[[item for item in sublist if item not in include_list] for sublist in change_list]
                utils.logger.debug("进行修改前change_list(删除原list中的same type+name)\n%s\n" % change_list)

            #对change_list去重
            change_list_uni=list(set(tuple(item) for item in change_list))
            change_list_uni=[list(item) for item in change_list_uni]
            change_list=change_list_uni
            utils.logger.debug("修改关联中change_list去重,before笛卡尔\n%s\n" % change_list)

            # 笛卡尔
            change_switch_product=list(itertools.product(*change_switch))
            final_product=[]
            for item in change_list:
                for sub in change_switch_product:
                    final_product.append(item+list(sub))
            change_list=final_product
            utils.logger.debug("修改后change_list(笛卡尔)\n%s\n" % change_list)



        # 处理增加关联
        if change_add[0]!=[]:
            utils.logger.debug("增加关联的方法!")
            ### merge change_list 和 change_add. 笛卡尔积两个list
            import itertools
            change_list = [a + b for a, b in itertools.product(change_list, change_add)]
            utils.logger.debug("笛卡尔积后的rule规则集合:\n%s\n" % change_list)

            # 对每个元素的元素去重
            result_change_list = []
            for sublist in change_list:
                if len(sublist) == len(set(sublist)):
                    # 子列表无重复，直接保留
                    result_change_list.append(sublist)
                else:
                    # 子列表有重复，仅保留其"无重复子集"
                    unique_subset = list(set(sublist))
                    if unique_subset not in result_change_list:  # 避免重复添加
                        result_change_list.append(unique_subset)
            change_list=result_change_list
            utils.logger.debug("去重之后rule规则集合:\n%s\n" % str(change_list))

            check_typename=[]
            check_typename_sub=[]
            for sublist in change_list:
                for item in sublist:
                    ans=models.get_info("*","id",item)
                    ans=ans[0]['type']+ans[0]['name']
                    check_typename_sub.append(ans)


                check_typename.append(check_typename)

            utils.logger.debug("去重之后rule规则集合(检查type和name):\n%s\n" % str(check_typename))

            # for sublist in check_typename:
            #     utils.logger.debug("检查合法性")
            #     seen_2=[]
            #     for i in sublist:
            #         if i in seen_2:
            #             utils.logger.debug("检查type和name不合法的添加方式！:\n%s\n" % str(sublist))
            #             return jsonify({"status": "fail", "message": "不合法的添加方式！"})
            #         else:
            #             seen_2.append(i)

        # 处理删除关联
        if final_delete:
            utils.logger.debug("删除关联的方法!")
            # 从 a 的每个子列表中移除 b 中的元素
            c = [[x for x in sublist if x not in final_delete] for sublist in change_list]

            change_list=c
        utils.logger.debug("处理change_delete之后rule规则集合:\n%s\n" % change_list)

        d = []
        for sublist in change_list:
            if sublist and sublist not in d:  # 保留非空且未重复的子列表
                d.append(sublist)
        change_list=d

        # 处理重复子集
        final_list = []
        for current in change_list:
            # 如果当前列表不是任何其他列表的子集，则保留
            if not any(set(current).issubset(set(other)) for other in change_list if current != other):
                final_list.append(current)
        utils.logger.debug("处理final_list规则集合:\n%s\n" % final_list)

        # #先删表，再插表
        # cur = pysql.CURSOR()
        # for item in change_list_id:
        #     sql="DELETE FROM rms_software_rule WHERE `id`=%s" % item['id']
        #     cur.delete(sql)

        # 转为@X@
        final_list = [",".join("@{}@".format(x) for x in sublist) for sublist in final_list]
        utils.logger.debug("插表final_list:\n%s\n" % final_list)

        # orgin_rule=[]
        cur = pysql.CURSOR()
        # for item in change_list_id:
        #     sql="select * from rms_software_rule where id =%s" % item['id']
        #     ans=cur.select_all(sql)[0]['rule']
        #     orgin_rule.append(ans)
        # cleaned_data = [item[0].split(',') for item in orgin_rule]

        change_list_origin = json.dumps(change_list_origin, ensure_ascii=False) # 转换为 JSON 字符串
        utils.logger.debug('\n【change_list_origin】变量的值为:\n%s' % change_list_origin)

        import time
        id=int(time.time()*1000)
        # log插表
        for item in final_list:
            insert_data={
                'rule':item,
                'user':session['logged_user_id'],
                'action_id':id,
                'action':"修改",
                "type":"修改规则",
                "time":utils.now(),
                "json_data":change_list_origin
            }
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)
        utils.logger.debug("\n插表（log）成功")

        #先删表，再插表
        cur = pysql.CURSOR()
        for item in change_list_id:
            sql="""UPDATE rms_software_rule  SET & WHERE @"""
            data={"action":"注销"}
            where={"id":item['id']}
            cur.update(sql,data,where)


        # 插表
        for item in final_list:
            insert_data={
                'rule':item
            }
            sql="INSERT INTO rms_software_rule (&) VALUES (@)"
            cur.insert(sql,insert_data)
        utils.logger.debug("\n插表成功")

        # 检查和清理子集规则
        deleted_rules = check_multiple_rule()
        utils.logger.debug(f"\n子集检查结果: 删除了{deleted_rules}条冗余规则")

        sql= "select * from rms_software_log where action_id='%s'" % str(id)
        result_list=cur.select_all(sql)
        new_list=[]
        rule_elements = []
        for rules in result_list:
                # 获取 rule 字段并去除首尾的 @ 符号
                rule_parts = rules['rule'].strip('@').split(',')

                # 去除每个部分两端的 @ 符号并将它们转换为整数
                rule_numbers = [int(part.strip('@')) for part in rule_parts]
                utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

                new_dict={}
                for num in rule_numbers:
                    # @17@ (str) -> 17 (int)
                    utils.logger.debug("num值:%s" % num)
                    num=int(str(num).replace('@',''))
                    info=models.get_info('*','id',num)
                    info=info[0]

                    # 添加到新dict（拼出展示的字符）

                    type_value = info['type']
                    name_value = info['name']
                    version_value = info['version']
                    new_dict.update({'id':rules['id'],'action_id':str(id),type_value: str(name_value) +' '+ str(version_value)})
                    utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
                new_list.append(new_dict)
                utils.logger.debug("modify（修改）中new_list数据结构:\n%s" %new_list)



        return jsonify({"status": "success", "message": "提交成功","data":new_list})

    except Exception as e:
        return jsonify({"status": "fail", "message": "存在或包含信息"})

#===============================================================================
################################################################################
# 修改单页
@uc.route('/single_modify_rule',methods=['POST'])
def single_modify_rule():
    cur = pysql.CURSOR()
    """
    接收单个规则修改的请求，处理修改数据

    请求数据格式:
    {
        "id": "123",
        "components": [
            {
                "type": "中间件",
                "name": "AMQ",
                "version": "5.18.3"
            },
            {
                "type": "CPU",
                "name": "芯片",
                "version": "海光"
            }
        ]
    }
    """
    try:
        # 获取请求中的JSON数据
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "未接收到数据"}), 400

        # 提取ID和组件数据
        item_id = data.get('id')
        components = data.get('components', [])

        if not item_id:
            return jsonify({"status": "error", "message": "缺少ID字段"}), 400

        if not components or not isinstance(components, list):
            return jsonify({"status": "error", "message": "组件数据格式错误"}), 400

        # 记录接收到的数据
        current_app.logger.info(f"接收到修改请求: ID={item_id}, 组件数据={components}")

        new_rule=""
        for item in components:
            #拼出新的规则
            node_id=models.get_info_id(str(item['type']),str(item['name']),str(item['version']))[0]['id']
            node_id_str="@"+str(node_id)+"@,"
            new_rule+=node_id_str

        new_rule = new_rule.rstrip(",")

        # 2. 按逗号分隔字符串
        parts = new_rule.split(",")

        # 3. 提取数字并排序
        sorted_parts = sorted(parts, key=lambda x: int(re.search(r'\d+', x).group()))

        # 4. 将排序后的部分重新拼接成字符串
        new_rule = ",".join(sorted_parts)

        # 检查是否有重复
        sql="""SELECT count(*) as c FROM rms_software_rule where (status='使用中' and action='正常' and rule='%s')""" % new_rule
        muti_count=cur.select_all(sql)[0]['c']
        if muti_count!=0:
            return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500
        sql="""select * from rms_software_rule where id=%s""" % item_id
        before_rule=cur.select_all(sql)[0]['rule']
        utils.logger.debug("单条修改前：\n%s"% before_rule)
        utils.logger.debug("单条修改后：\n%s"% new_rule)

        sql="""UPDATE rms_software_rule  SET & WHERE @"""
        data={"rule":new_rule}
        where={"id":item_id}

        cur.update(sql,data,where)
        import time
        id=int(time.time()*1000)
        insert_data={
                'rule':before_rule,
                'user':session['logged_user_id'],
                'action_id':id,
                'action':"修改",
                "type":"修改规则",
                "time":utils.now(),
                "json_data":[new_rule],
                "rule_id":item_id
        }
        sql="INSERT INTO rms_software_log (&) VALUES (@)"
        cur.insert(sql,insert_data)

        check_multiple_rule()
        # 返回成功响应
        return jsonify({
            "status": "success",
            "message": "规则修改成功",
            "data": {
                "id": item_id,
                "components": components
            }
        })

    except Exception as e:
        # 记录异常信息
        current_app.logger.error(f"处理修改请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500

@uc.route('/single_delete_rule',methods=['POST'])
def single_delete_rule():
    cur = pysql.CURSOR()
    """
    接收单个规则删除的请求,删除数据(实际上是注销)

    请求数据格式:
    {
        "id": "123",
    }
    """
    try:

        data = request.get_json()
        item_id=data.get('id')
        current_app.logger.info(f"接收到删除请求: ID={item_id}")
        # 验证是否存在
        sql="""select count(*) AS c from rms_software_rule where id =%s""" % item_id
        count_check=cur.select_all(sql)[0]['c']
        if count_check!=1:
            return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500
        else:
            sql="""select * from rms_software_rule where id=%s""" % item_id
            before_rule=cur.select_all(sql)[0]['rule']
            import time
            id=int(time.time()*1000)
            insert_data={
                    'rule':before_rule,
                    'user':session['logged_user_id'],
                    'action_id':id,
                    'action':"修改",
                    "type":"修改规则",
                    "time":utils.now(),
                    "rule_id":item_id
            }
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)

            sql="""UPDATE rms_software_rule  SET & WHERE @"""
            data={"action":"注销"}
            where={"id":item_id}
            cur.update(sql,data,where)
            check_multiple_rule()

            # 返回成功响应
            return jsonify({
                "status": "success",
                "message": "规则删除成功",
                "data": {
                    "id": item_id,
                }
            })
    except Exception as e:
        # 记录异常信息
        current_app.logger.error(f"处理修改请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500


@uc.route('/single_outline_rule',methods=['POST'])
def single_outline_rule():
    cur = pysql.CURSOR()
    """
    接收单个规则删除的请求,删除数据(实际上是注销)

    请求数据格式:
    {
        "id": "123",
    }
    """
    try:

        data = request.get_json()
        item_id=data.get('id')
        current_app.logger.info(f"接收到注销请求: ID={item_id}")
        # 验证是否存在
        sql="""select count(*) AS c from rms_software_rule where id =%s""" % item_id
        count_check=cur.select_all(sql)[0]['c']
        if count_check!=1:
            return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500
        else:
            sql="""select * from rms_software_rule where id=%s""" % item_id
            before_rule=cur.select_all(sql)[0]['rule']
            import time
            id=int(time.time()*1000)
            insert_data={
                    'rule':before_rule,
                    'user':session['logged_user_id'],
                    'action_id':id,
                    'action':"注销",
                    "type":"注销规则",
                    "time":utils.now(),
                    "rule_id":item_id
            }
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)

            sql="""UPDATE rms_software_rule  SET & WHERE @"""
            data={"status":"注销"}
            where={"id":item_id}
            cur.update(sql,data,where)
            check_multiple_rule()

            # 返回成功响应
            return jsonify({
                "status": "success",
                "message": "规则注销成功",
                "data": {
                    "id": item_id,
                }
            })
    except Exception as e:
        # 记录异常信息
        current_app.logger.error(f"处理修改请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器错误: {str(e)}"}), 500



def error_page(error, code=500):
    """
    通用错误处理函数，返回友好的错误页面

    Args:
        error: 错误对象或错误消息
        code: HTTP状态码，默认为500

    Returns:
        渲染后的错误页面
    """
    error_type = type(error).__name__ if not isinstance(error, str) else "Error"
    error_message = str(error)

    # 根据错误类型设置不同的错误消息
    if 'database' in error_message.lower() or 'db' in error_message.lower():
        error_title = '数据库操作错误'
        error_message = '执行数据库操作时发生错误，请检查您的输入数据并重试。'
    elif 'permission' in error_message.lower() or 'access' in error_message.lower():
        error_title = '权限错误'
        error_message = '您没有执行此操作的权限，请联系管理员获取适当的访问权限。'
    elif 'timeout' in error_message.lower():
        error_title = '请求超时'
        error_message = '操作请求超时，请稍后重试。'
    elif 'not found' in error_message.lower():
        code = 404
        error_title = '资源未找到'
        error_message = '请求的资源不存在或已被移除。'
    elif 'validation' in error_message.lower() or 'invalid' in error_message.lower():
        error_title = '数据验证错误'
        error_message = '提交的数据无效，请检查并修正您的输入。'
    else:
        error_title = '操作错误'
        if not error_message or error_message == 'None':
            error_message = '处理您的请求时发生了错误，请稍后重试。'

    # 记录错误到日志
    current_app.logger.error(f"Error {code}: {error_type} - {str(error)}")

    # 渲染错误页面
    return render_template(
        'components/error_page.html',
        error_title=error_title,
        error_message=error_message,
        error_details=str(error),
        error_code=f"错误 {code}"
    ), code

@uc.route('/software_pre_check', methods=['POST'])
def software_pre_check():
    try:
        data = request.data
        data = json.loads(data)
        utils.logger.debug("\nsoftware_pre_check传参:\n%s\n" % data)

        # 检查操作类型
        operation_type = data.get('operation_type', 'add')
        preview_results = []

        if operation_type == 'add':
            # 处理添加规则的预览逻辑
            rules = data.get('rules', [])
            # 预处理规则数据
            add_rule_list = []

            for item in rules:
                # 处理"全部"版本
                for i in range(len(item['version'])):
                    if item['version'][i] == '全部':
                        item['version'] = ['全部']
                        break

                # 构建每个规则的ID列表
                rule_ids = []
                cur = pysql.CURSOR()
                for ver in item['version']:
                    if ver != '全部':
                        sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s AND `version`=%s \
                            AND (action='正常' and status='使用中')" % (
                            "'"+item['type']+"'", "'"+item['name']+"'", "'"+ver+"'")
                        ans = cur.select_all(sql)
                        if ans:
                            rule_ids.append(ans[0]['id'])
                    else:  # 全部版本
                        sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s \
                            AND (action='正常' and status='使用中')" % (
                            "'"+item['type']+"'", "'"+item['name']+"'")
                        ans = cur.select_all(sql)
                        for i in ans:
                            rule_ids.append(i['id'])

                if rule_ids:
                    add_rule_list.append(rule_ids)

            # 生成笛卡尔积
            import itertools
            if add_rule_list:
                all_combinations = list(itertools.product(*add_rule_list))

                # 为每个组合生成预览数据
                for idx, combination in enumerate(all_combinations):
                    preview_dict = {'id': f'preview_{idx}'}

                    for id_num in combination:
                        info = models.get_info('*', 'id', id_num)
                        if info:
                            info = info[0]
                            type_value = info['type']
                            name_value = info['name']
                            version_value = info['version']
                            preview_dict[type_value] = f"{name_value} {version_value}"

                    preview_results.append(preview_dict)

        elif operation_type == 'modify':
            import itertools
            # 获取要修改的行和修改规则
            change_list_id = data.get('rowsToChange', [])
            modify_list = data.get('modifyData', [])

            # 预处理修改规则
            for item in modify_list:
                for i in range(len(item['version'])):
                    if item['version'][i] == '全部':
                        item['version'] = ['全部']
                        break

            # 处理分类
            change_add = []  # 增加关联
            change_switch = []  # 修改关联
            final_delete = []  # 删除关联

            cur = pysql.CURSOR()

            # 处理增加关联
            for item in modify_list:
                if item['relation'] == '增加关联':
                    change_add_before = []
                    for ver in item['version']:
                        if ver != '全部':
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s AND `version`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'", "'"+ver+"'")
                            ans = cur.select_all(sql)
                            if ans:
                                change_add_before.append(ans[0]['id'])
                        else:  # 全部版本
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'")
                            ans = cur.select_all(sql)
                            for i in ans:
                                change_add_before.append(i['id'])

                    if change_add_before:
                        change_add.append(change_add_before)

            # 处理修改关联
            include_ids_by_type = {}  # 按类型存储需要移除的ID
            for item in modify_list:
                if item['relation'] == '修改关联':
                    change_switch_before = []
                    item_type = item['type']

                    # 获取要修改的所有版本ID
                    for ver in item['version']:
                        if ver != '全部':
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s AND `version`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'", "'"+ver+"'")
                            ans = cur.select_all(sql)
                            if ans:
                                change_switch_before.append(ans[0]['id'])
                        else:  # 全部版本
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'")
                            ans = cur.select_all(sql)
                            for i in ans:
                                change_switch_before.append(i['id'])

                    # 获取该类型的所有ID（用于从原规则中删除）
                    if change_switch_before:
                        sql = "select * from rms_software_info WHERE `type`=%s \
                            AND (action='正常' and status='使用中')"  % (
                            "'"+item['type']+"'")
                        all_type_ids = cur.select_all(sql)
                        include_ids = [i['id'] for i in all_type_ids]
                        include_ids_by_type[item_type] = include_ids

                        change_switch.append(change_switch_before)

            # 处理删除关联
            for item in modify_list:
                if item['relation'] == '删除关联':
                    delete_ids = []
                    for ver in item['version']:
                        if ver != '全部':
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s AND `version`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'", "'"+ver+"'")
                            ans = cur.select_all(sql)
                            if ans:
                                delete_ids.append(ans[0]['id'])
                        else:  # 全部版本
                            sql = "select * from rms_software_info WHERE `type`=%s AND `name`=%s \
                                AND (action='正常' and status='使用中')" % (
                                "'"+item['type']+"'", "'"+item['name']+"'")
                            ans = cur.select_all(sql)
                            for i in ans:
                                delete_ids.append(i['id'])

                    final_delete.extend(delete_ids)

            # 去重删除ID
            final_delete = list(dict.fromkeys(final_delete))

            # 获取原始规则数据
            change_list = []
            for row in change_list_id:
                sql = "select * from rms_software_rule WHERE (status='使用中' and action='正常' and id=%s)" % row['id']
                rule_data = cur.select_all(sql)
                if rule_data:
                    rule_by_id = rule_data[0]['rule']

                    # 解析规则ID
                    rule_ids = [int(x[1:-1]) for x in rule_by_id.split(',')
                              if x.strip().startswith('@') and x.strip().endswith('@')]
                    change_list.append(rule_ids)

            # 处理修改关联 - 移除相同类型的组件，然后添加新的组合
            if change_switch:
                # 处理每种需要修改的类型
                for item_type, include_ids in include_ids_by_type.items():
                    # 从每个规则中移除该类型的所有组件
                    change_list = [[item for item in sublist if not is_id_of_type(item, item_type, cur)]
                                  for sublist in change_list]

                # 对change_list去重
                unique_rules = set(tuple(sorted(rule)) for rule in change_list)
                change_list = [list(rule) for rule in unique_rules if rule]  # 移除空规则

                # 为每个类型生成笛卡尔积并与原规则合并
                for switch_ids in change_switch:
                    new_change_list = []
                    for rule in change_list:
                        for switch_id in switch_ids:
                            new_rule = rule + [switch_id]
                            new_change_list.append(new_rule)
                    change_list = new_change_list

            # 处理增加关联 - 与原规则进行笛卡尔积
            if change_add:
                # 生成笛卡尔积
                all_add_combinations = list(itertools.product(*change_add))

                new_change_list = []
                for rule in change_list:
                    for add_combo in all_add_combinations:
                        new_rule = rule + list(add_combo)
                        # 去重
                        new_rule = list(dict.fromkeys(new_rule))
                        new_change_list.append(new_rule)

                change_list = new_change_list

            # 处理删除关联 - 从规则中移除指定ID
            if final_delete:
                change_list = [[x for x in rule if x not in final_delete] for rule in change_list]
                # 移除空规则和重复规则
                change_list = [rule for rule in change_list if rule]
                unique_rules = set(tuple(sorted(rule)) for rule in change_list)
                change_list = [list(rule) for rule in unique_rules]

            # 生成预览数据
            import time
            preview_id_base = int(time.time())

            for idx, rule_ids in enumerate(change_list):
                preview_dict = {'id': f'preview_{preview_id_base + idx}'}

                # 获取每个组件的详细信息
                for id_num in rule_ids:
                    info = models.get_info('*', 'id', id_num)
                    if info:
                        info = info[0]
                        type_value = info['type']
                        name_value = info['name']
                        version_value = info['version']

                        preview_dict[type_value] = f"{name_value} {version_value}"

                preview_results.append(preview_dict)

        return jsonify({"status": "success", "message": "预览生成成功", "data": preview_results})

    except Exception as e:
        utils.logger.error(f"预览生成错误: {str(e)}")
        return jsonify({"status": "error", "message": f"预览生成失败: {str(e)}"})

# 辅助函数：检查ID是否属于指定类型
def is_id_of_type(id_num, type_name, cursor):
    info = models.get_info('type', 'id', id_num)
    if info and info[0]['type'] == type_name:
        return True
    return False

# 检查和删除子集规则
def check_multiple_rule():
    """
    检查并删除数据库中被其他规则完全包含的子集规则
    例如：如果规则A的所有元素都包含在规则B中，则删除规则A

    此函数应在增加、修改、删除规则操作后调用，确保数据库中不存在冗余的子集规则
    """
    try:
        cur = pysql.CURSOR()

        # 第一步：获取所有正常状态的规则
        sql = "SELECT id, rule FROM rms_software_rule WHERE (status='使用中' and action='正常')"
        rules = cur.select_all(sql)

        if not rules or len(rules) <= 1:
            utils.logger.debug("规则数量不足，无需检查子集")
            return 0

        # 第二步：解析规则，构建规则ID到组件集合的映射
        rule_map = {}
        for rule_data in rules:
            rule_id = rule_data['id']
            rule_str = rule_data.get('rule', '')
            if not rule_str:
                continue

            # 解析规则字符串，提取组件ID
            components = set()
            parts = rule_str.split(',')
            for part in parts:
                if part.strip().startswith('@') and part.strip().endswith('@'):
                    try:
                        comp_id = int(part.strip('@'))
                        components.add(comp_id)
                    except (ValueError, TypeError):
                        continue

            if components:  # 只有组件集非空时才添加到映射
                rule_map[rule_id] = components

        if len(rule_map) <= 1:
            utils.logger.debug("有效规则数量不足，无需检查子集")
            return 0

        # 第三步：查找子集规则
        subsets_to_delete = []

        for rule_id, components in rule_map.items():
            for other_id, other_components in rule_map.items():
                # 跳过自身比较
                if rule_id == other_id:
                    continue

                # 如果当前规则是另一个规则的子集且组件数量较少，则标记为删除
                if components.issubset(other_components) and len(components) < len(other_components):
                    subsets_to_delete.append(rule_id)
                    break

        # 第四步：删除子集规则
        if subsets_to_delete:
            utils.logger.debug(f"发现{len(subsets_to_delete)}个子集规则需要删除: {subsets_to_delete}")

            # 构建IN子句的id列表
            id_list = ','.join(str(id) for id in subsets_to_delete)

            # 执行删除操作
            if id_list:
                del_sql = f"DELETE FROM rms_software_rule WHERE id IN ({id_list})"
                deleted = cur.delete(del_sql)
                utils.logger.debug(f"成功删除{deleted}个子集规则")
                return deleted
        else:
            utils.logger.debug("未发现子集规则，无需删除")

        return 0

    except Exception as e:
        utils.logger.error(f"检查子集规则时出错: {str(e)}")
        return 0

@uc.route('/software_info_outline',methods=['POST','GET'])
def software_info_outline():
    try:
        cur = pysql.CURSOR()
        import time
        action_id=int(time.time()*1000)
        data = request.get_json()
        utils.logger.debug(f"获取下线软件信息: {data}")
        type = data.get('type')
        name = data.get('name')
        version = data.get('version')
        id = data.get('id')
        user_select = data.get('user_select')

        utils.logger.debug(f"获取到的字段 - 类型: {type}, 名称: {name}, 版本: {version}, id: {id}, 用户选择: {user_select}")
        all_id=[]
        if user_select == 'version':
            all_id.append(id)

            sql="""UPDATE rms_software_info  SET & WHERE @"""
            data={"status":"注销"}
            where={"id":id}
            cur.update(sql,data,where)


            insert_data={
                    'user':session['logged_user_id'],
                    'action_id':action_id,
                    'action':"注销",
                    "type":"注销组件",
                    "time":utils.now(),
                    "info_id":id,
                    "before_type":type,
                    "before_name":name,
                    "before_version":version
            }
            sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
            cur.insert(sql,insert_data)



        elif user_select == 'name':
            sql="""select * from rms_software_info WHERE `name`='%s' AND `type`='%s' and (action='正常' and status='使用中')"""% (name,type)
            all_data=cur.select_all(sql)
            for i in all_data:
                all_id.append(i['id'])
                insert_data={
                        'user':session['logged_user_id'],
                        'action_id':action_id,
                        'action':"注销",
                        "type":"注销组件",
                        "time":utils.now(),
                        "info_id":i['id'],
                        "before_type":i['type'],
                        "before_name":i['name'],
                        "before_version":i['version']
                }
                sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
                cur.insert(sql,insert_data)
            sql="""UPDATE rms_software_info  SET & WHERE (action='正常' and status='使用中') AND @"""
            data={"status":"注销"}
            where={"name":name,"type":type}
            cur.update(sql,data,where)

        elif user_select == 'type':
            sql="""select * from rms_software_info WHERE `type`='%s' and (action='正常' and status='使用中')"""% (type)
            all_data=cur.select_all(sql)
            for i in all_data:
                all_id.append(i['id'])
                insert_data={
                        'user':session['logged_user_id'],
                        'action_id':action_id,
                        'action':"注销",
                        "type":"注销组件",
                        "time":utils.now(),
                        "info_id":i['id'],
                        "before_type":i['type'],
                        "before_name":i['name'],
                        "before_version":i['version']
                }
                sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
                cur.insert(sql,insert_data)
            sql="""UPDATE rms_software_info  SET & WHERE (action='正常' and status='使用中') AND @"""
            data={"status":"注销"}
            where={"type":type}
            cur.update(sql,data,where)
        else:
            return jsonify({"status": "error", "message": "用户选择错误"})
        utils.logger.debug(f"删除的id: {all_id}")
        # 将all_id中的每个元素用"@"包裹起来
        formatted_ids = [f"@{str(my_id)}@" for my_id in all_id]
        utils.logger.debug(f"格式化后的ID字符串: {formatted_ids}")

        # 创建一个单独的连接对象用于直接执行SQL
        conditions = ["rule LIKE '%{}%'".format(i.replace("'", "''")) for i in formatted_ids]
        sql = "SELECT * FROM rms_software_rule WHERE (status='使用中' and action='正常') AND {} ".format(" OR ".join(conditions))
        utils.logger.debug(f"sql是: {sql}")
        all_list=cur.select_all(sql)
        for i in all_list:
            rule = i['rule'].split(",")
            utils.logger.debug(f"rule是: {rule}")
            utils.logger.debug(f"formatted_ids是: {formatted_ids}")
            new_rule=list(set(rule)-set(formatted_ids))
            utils.logger.debug(f"new_rule是: {new_rule}")
            new_rule_str=','.join(new_rule)
            utils.logger.debug(f"new_rule_str: {new_rule_str}")

            insert_data={
                    'user':session['logged_user_id'],
                    'action_id':action_id,
                    'action':"注销",
                    "type":"注销规则",
                    "time":utils.now(),
                    "rule_id":i['id'],
                    "rule":new_rule_str,
                    "rule_before":i['rule']
            }
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)

            sql="""UPDATE rms_software_rule SET & WHERE @"""
            data={"rule":new_rule_str}
            where={"id":i['id']}
            cur.update(sql,data,where)
        check_multiple_rule()
    except Exception as e:
        utils.logger.error(f"处理请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"处理请求时出错: {str(e)}"}), 500
    return jsonify({"status": "success", "message": "注销成功"})

@uc.route('/software_info_delete',methods=['POST','GET'])
def software_info_delete():
    try:
        cur = pysql.CURSOR()
        import time
        action_id=int(time.time()*1000)
        data = request.get_json()
        utils.logger.debug(f"获取下线软件信息: {data}")
        type = data.get('type')
        name = data.get('name')
        version = data.get('version')
        id = data.get('id')
        user_select = data.get('user_select')

        utils.logger.debug(f"获取到的字段 - 类型: {type}, 名称: {name}, 版本: {version}, id: {id}, 用户选择: {user_select}")
        all_id=[]
        if user_select == 'version':
            all_id.append(id)
            sql="""UPDATE rms_software_info  SET & WHERE @"""
            data={"action":"注销"}
            where={"id":id}
            cur.update(sql,data,where)

            insert_data={
                    'user':session['logged_user_id'],
                    'action_id':action_id,
                    'action':"删除",
                    "type":"删除组件",
                    "time":utils.now(),
                    "info_id":id,
                    "before_type":type,
                    "before_name":name,
                    "before_version":version
            }
            sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
            cur.insert(sql,insert_data)


        elif user_select == 'name':
            sql="""select * from rms_software_info WHERE `name`='%s' AND `type`='%s' and (action='正常' and status='使用中')"""% (name,type)
            all_data=cur.select_all(sql)
            for i in all_data:
                all_id.append(i['id'])
                insert_data={
                        'user':session['logged_user_id'],
                        'action_id':action_id,
                        'action':"删除",
                        "type":"删除组件",
                        "time":utils.now(),
                        "info_id":i['id'],
                        "before_type":i['type'],
                        "before_name":i['name'],
                        "before_version":i['version']
                }
                sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
                cur.insert(sql,insert_data)


            sql="""UPDATE rms_software_info  SET & WHERE (action='正常' and status='使用中') AND @"""
            data={"action":"注销"}
            where={"name":name,"type":type}
            cur.update(sql,data,where)




        elif user_select == 'type':
            sql="""select * from rms_software_info WHERE `type`='%s' and (action='正常' and status='使用中')"""% (type)
            all_data=cur.select_all(sql)
            for i in all_data:
                all_id.append(i['id'])
                insert_data={
                        'user':session['logged_user_id'],
                        'action_id':action_id,
                        'action':"删除",
                        "type":"删除组件",
                        "time":utils.now(),
                        "info_id":i['id'],
                        "before_type":i['type'],
                        "before_name":i['name'],
                        "before_version":i['version']
                }
                sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
                cur.insert(sql,insert_data)

            sql="""UPDATE rms_software_info  SET & WHERE (action='正常' and status='使用中') AND @"""
            data={"action":"注销"}
            where={"type":type}
            cur.update(sql,data,where)
        else:
            return jsonify({"status": "error", "message": "用户选择错误"})
        utils.logger.debug(f"删除的id: {all_id}")
        # 将all_id中的每个元素用"@"包裹起来
        formatted_ids = [f"@{str(my_id)}@" for my_id in all_id]
        utils.logger.debug(f"格式化后的ID字符串: {formatted_ids}")

        # 创建一个单独的连接对象用于直接执行SQL
        conditions = ["rule LIKE '%{}%'".format(i.replace("'", "''")) for i in formatted_ids]
        sql = "SELECT * FROM rms_software_rule WHERE (status='使用中' and action='正常') AND {} ".format(" OR ".join(conditions))
        utils.logger.debug(f"sql是: {sql}")
        all_list=cur.select_all(sql)
        for i in all_list:
            rule = i['rule'].split(",")
            utils.logger.debug(f"rule是: {rule}")
            utils.logger.debug(f"formatted_ids是: {formatted_ids}")
            new_rule=list(set(rule)-set(formatted_ids))
            utils.logger.debug(f"new_rule是: {new_rule}")
            new_rule_str=','.join(new_rule)
            utils.logger.debug(f"new_rule_str: {new_rule_str}")

            insert_data={
                    'user':session['logged_user_id'],
                    'action_id':action_id,
                    'action':"删除",
                    "type":"删除规则",
                    "time":utils.now(),
                    "rule_id":i['id'],
                    "rule":new_rule_str,
                    "rule_before":i['rule'],
            }
            sql="INSERT INTO rms_software_log (&) VALUES (@)"
            cur.insert(sql,insert_data)

            sql="""UPDATE rms_software_rule SET & WHERE @"""
            data={"rule":new_rule_str}
            where={"id":i['id']}
            cur.update(sql,data,where)
        check_multiple_rule()
    except Exception as e:
        utils.logger.error(f"处理请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"处理请求时出错: {str(e)}"}), 500
    return jsonify({"status": "success", "message": "注销成功"})


@uc.route('/software_info_add',methods=['POST','GET'])
def software_info_add():
    try:
        cur = pysql.CURSOR()
        data = request.get_json()
        utils.logger.debug(f"获取添加的软件信息: {data}")
        data=data[0]
        type=data.get('type')
        name=data.get('name')
        version=data.get('version')

        # 预检查
        sql="""select count(*) AS count from rms_software_info WHERE `type`='%s' and `name`='%s' AND `version`='%s' and (action='正常' and status='使用中')"""% (name,type,version)
        if_have=cur.select_all(sql)[0]['count']
        if if_have>0:
            return jsonify({"status": "error", "message": "软件已存在"})

        sql="""INSERT INTO rms_software_info (&) VALUES (@)"""
        data={"type":type,"name":name,"version":version}
        atom_id=cur.insert(sql,data)

        import time
        id=int(time.time()*1000)
        insert_data={

                'user':session['logged_user_id'],
                'action_id':id,
                'action':"增加",
                "type":"增加组件",
                "time":utils.now(),
                "info_id":atom_id,
                "before_type":type,
                "before_name":name,
                "before_version":version
        }
        sql="INSERT INTO rms_software_info_log (&) VALUES (@)"
        cur.insert(sql,insert_data)


    except Exception as e:
        utils.logger.error(f"处理请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"处理请求时出错: {str(e)}"}), 500
    return jsonify({"status": "success", "message": "添加成功"})



@uc.route('/software_info_change',methods=['POST','GET'])
def software_info_change():
    try:
        import time
        action_id=int(time.time()*1000)
        cur = pysql.CURSOR()
        data = request.get_json()
        utils.logger.debug(f"获取修改的软件信息: {data}")
        # 提取请求数据
        id = data.get('id')
        original_data = data.get('original', {})
        updated_data = data.get('updated', {})
        node_type = data.get('node_type')

        # 提取原始值
        original_type = original_data.get('type')
        original_name = original_data.get('name')
        original_version = original_data.get('version')

        # 提取更新值
        updated_type = updated_data.get('type')
        updated_name = updated_data.get('name')
        updated_version = updated_data.get('version')

        utils.logger.debug(f"修改ID: {id}")
        utils.logger.debug(f"原始值 - 类型: {original_type}, 名称: {original_name}, 版本: {original_version}")
        utils.logger.debug(f"更新值 - 类型: {updated_type}, 名称: {updated_name}, 版本: {updated_version}")
        utils.logger.debug(f"修改节点类型: {node_type}")

        import time
        action_id=int(time.time()*1000)


        if node_type == 'type':
            sql="""select * from rms_software_info where type='%s' and (action='正常' and status='使用中')"""% (original_type)
            info=cur.select_all(sql)
            for i in info:
                sql="""insert into rms_software_info_log (&) values (@)"""
                data={
                    "user":session['logged_user_id'],
                    "action_id":action_id,
                    "action":"修改",
                    "type":"修改组件",
                    "time":utils.now(),
                    "info_id":i['id'],
                    "before_type":i['type'],
                    "before_name":i['name'],
                    "before_version":i['version'],
                    "after_type":updated_type,
                    "after_name":i['name'],
                    "after_version":i['version']
                }
                cur.insert(sql,data)
            sql="""UPDATE rms_software_info SET & WHERE @"""
            data={"type":updated_type}
            where={"type":original_type}
            cur.update(sql,data,where)



        elif node_type == 'name':
            sql="""select * from rms_software_info where name='%s' and type='%s' and (action='正常' and status='使用中')"""% (original_name,original_type)
            info=cur.select_all(sql)
            for i in info:
                sql="""insert into rms_software_info_log (&) values (@)"""
                data={
                    "user":session['logged_user_id'],
                    "action_id":action_id,
                    "action":"修改",
                    "type":"修改组件",
                    "time":utils.now(),
                    "info_id":i['id'],
                    "before_type":i['type'],
                    "before_name":i['name'],
                    "before_version":i['version'],
                    "after_type":updated_type,
                    "after_name":updated_name,
                    "after_version":i['version']
                }
                cur.insert(sql,data)

            sql="""UPDATE rms_software_info SET & WHERE @"""
            data={"type":updated_type,"name":updated_name}
            where={"type":original_type,"name":original_name}
            cur.update(sql,data,where)



        elif node_type == 'version':
            sql="""select * from rms_software_info where name='%s' and type='%s' and version='%s' and (action='正常' and status='使用中')"""% (original_name,original_type,original_version)
            info=cur.select_all(sql)
            for i in info:
                sql="""insert into rms_software_info_log (&) values (@)"""
                data={
                    "user":session['logged_user_id'],
                    "action_id":action_id,
                    "action":"修改",
                    "type":"修改组件",
                    "time":utils.now(),
                    "info_id":i['id'],
                    "before_type":i['type'],
                    "before_name":i['name'],
                    "before_version":i['version'],
                    "after_type":updated_type,
                    "after_name":updated_name,
                    "after_version":updated_version
                }
                cur.insert(sql,data)

            sql="""UPDATE rms_software_info SET & WHERE @"""
            data={"type":updated_type,"name":updated_name,"version":updated_version}
            where={"type":original_type,"name":original_name,"version":original_version}
            cur.update(sql,data,where)
        else:
            return jsonify({"status": "error", "message": "用户选择错误"})


    except Exception as e:
        utils.logger.error(f"处理请求时出错: {str(e)}")
        return jsonify({"status": "error", "message": f"处理请求时出错: {str(e)}"}), 500
    return jsonify({"status": "success", "message": "修改成功"})

# 规则验证：
@uc.route('/cts_software_rule_check',methods=['POST','GET'])
def cts_software_rule_check():

    data = request.data
    data=data = json.loads(data)

    #需要找的id_list
    find_list=[]

    # 查找每个row对应的编号，item对应data=[{},{},{}]中{}
    for item in data:
        #每次loop应该重置
        software_type=''
        software_name=''
        software_version=''
        #先判断全不是 全部 的情况==skip
        if item['type']=='全部' and item['name']=='全部' and item['version']=='全部':
            utils.logger.debug("全不是全部")
            cur = pysql.CURSOR()
            sql_check = "SELECT * FROM rms_software_rule where status='使用中' and action='正常'"
            # 执行查询
            result_list=cur.select_all(sql_check)

            new_list=[]
            rule_elements = []
            for rules in result_list:
                    # 获取 rule 字段并去除首尾的 @ 符号
                    rule_parts = rules['rule'].strip('@').split(',')

                    # 去除每个部分两端的 @ 符号并将它们转换为整数
                    rule_numbers = [int(part.strip('@')) for part in rule_parts]
                    utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

                    new_dict={}
                    for num in rule_numbers:
                        # @17@ (str) -> 17 (int)
                        utils.logger.debug("num值:%s" % num)
                        num=int(str(num).replace('@',''))
                        info=models.get_info('*','id',num)
                        info=info[0]

                        # 添加到新dict（拼出展示的字符）

                        type_value = info['type']
                        name_value = info['name']
                        version_value = info['version']
                        new_dict.update({'id':rules['id'],type_value: str(name_value) +' '+ str(version_value)})
                        utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
                    new_list.append(new_dict)
                    utils.logger.debug("new_list数据结构:\n%s" %new_list)

            # 构建返回结果
            if new_list:
                return jsonify({
                    "rules": new_list,
                    "check_status": True,
                    "total_num": len(new_list),
                    "message": f"查询到 {len(new_list)} 条关联软件规则"
                })
            else:
                return jsonify({
                    "rules": [],
                    "check_status": False,
                    "total_num": 0,
                    "message": "无关联软件"
                })

        else:

            # type没有全部的可能，type为全部则name\ version也是全部
            software_type="'"+item['type']+"'"

            if item['name']!='全部':
                software_name="'"+item['name']+"'"
            if item['name']=='全部':
                #name为全部则version也是全部
                utils.logger.debug("怎么错了2？\n%s"% item)
                name_list=models.get_info('`id`','`type`',"'"+item['type']+"'")

                utils.logger.debug("name_list\n%s"% name_list)

                if not find_list:
                    find_list =[[sub_item['id']] for sub_item in name_list]
                else:

                    utils.logger.debug("find_list\n%s\n"% find_list)

                    find_list = [sublist + [sub_item['id']] for sublist in find_list for sub_item in name_list]

            utils.logger.debug("怎么错了？\n%s"% item)
            if item['version']!='全部':
                software_version="'"+item['version']+"'"
            if item['version']=='全部':
                if item['name']=='全部':
                    #无事发生，因为'name'添加过这种可能
                    find_list=find_list
                else:
                    version_list=models.get_info('`id`','`name`',"'"+item['name']+"'")
                    utils.logger.debug("version_list\n%s"% version_list)
                    if not find_list:
                        find_list =[[sub_item['id']] for sub_item in version_list]
                    else:
                        find_list = [sublist + [sub_item['id']] for sublist in find_list for sub_item in version_list]

            if software_type!='' and software_name!='' and software_version!='':
                cur = pysql.CURSOR()
                sql="select `id` from rms_software_info \
                    WHERE `type`=%s \
                    AND `name`=%s \
                    AND `version`=%s \
                    AND (action='正常' and status='使用中')" % (software_type,software_name,software_version)
                ans=cur.select_all(sql)
                

                # 检查查询结果是否为空
                if not ans:
                    return jsonify({
                        "rules": [],
                        "check_status": False,
                        "total_num": 0,
                        "message": f"在rms_software_info表中未找到匹配的软件信息: type={software_type}, name={software_name}, version={software_version}"
                    })

                if find_list!=[]:
                    utils.logger.debug("三非无find_list\n%s"% find_list)
                    for i in find_list:
                        i.extend([d['id'] for d in ans])

                else:
                    find_list.append([d['id'] for d in ans])

    #查询list，去rms_software_rule查找对应的表
    #find_list = list(set(find_list))
    utils.logger.debug("用户需要查询的find_list:\n%s" % find_list)

    # 生成 SQL 查询
    conditions = []
    for sub_find_list in find_list:
        condition = " AND ".join(["rule LIKE '%@" + str(item) + "@%'" for item in sub_find_list])
        conditions.append("(" + condition + ")")

    cur = pysql.CURSOR()
    if conditions:
        sql_check = '''
            SELECT * FROM rms_software_rule
            WHERE (status = '使用中' and action = '正常')
            AND ({})
        '''.format(" OR ".join(conditions))
    else:
        sql_check = '''
            SELECT * FROM rms_software_rule
            WHERE (status = '使用中' and action = '正常')
        '''
    utils.logger.debug("sql模糊查询语句：\n%s"%sql_check)
    # 执行查询
    result_list=cur.select_all(sql_check)
    # result_list 如下[{u'id': 1, u'rule': u'@1@,@4@,@7@,@14@'}]


    new_list=[]
    rule_elements = []
    for rules in result_list:
        # 获取 rule 字段并去除首尾的 @ 符号
        rule_parts = rules['rule'].strip('@').split(',')

        # 去除每个部分两端的 @ 符号并将它们转换为整数
        rule_numbers = [int(part.strip('@')) for part in rule_parts]
        utils.logger.debug("rule_numbers值:\n%s" % rule_numbers)

        new_dict={}
        for num in rule_numbers:
            # @17@ (str) -> 17 (int)
            utils.logger.debug("num值:%s" % num)
            num=int(str(num).replace('@',''))
            info=models.get_info('*','id',num)
            info=info[0]

            # 添加到新dict（拼出展示的字符）

            type_value = info['type']
            name_value = info['name']
            version_value = info['version']
            new_dict.update({'id':rules['id'],type_value: str(name_value) +' '+ str(version_value)})
            utils.logger.debug("new_dict数据结构:\n%s" %new_dict)
        new_list.append(new_dict)
        utils.logger.debug("new_list数据结构:\n%s" %new_list)

    # 构建返回结果
    if new_list:
        return jsonify({
            "rules": new_list,
            "check_status": True,
            "total_num": len(new_list),
            "message": f"查询到 {len(new_list)} 条关联软件规则"
        })
    else:
        return jsonify({
            "rules": [],
            "check_status": False,
            "total_num": 0,
            "message": "无关联软件"
        })


# 软件信息查询：
@uc.route('/cts_software_info_check',methods=['POST','GET'])
def cts_software_info_check():
    """
    查询rms_software_info表中的软件信息
    输入字段：type, name, version（字段可以为空）
    输出：表返回结果、总数、状态和报错信息
    """
    try:
        data = request.data
        data = json.loads(data)

        # 获取查询参数
        software_type = data.get('type', '').strip()
        software_name = data.get('name', '').strip()
        software_version = data.get('version', '').strip()

        utils.logger.debug(f"软件信息查询参数: type={software_type}, name={software_name}, version={software_version}")

        # 构建SQL查询条件
        cur = pysql.CURSOR()
        conditions = ["action='正常'", "status='使用中'"]
        params = []

        if software_type:
            conditions.append("type=%s")
            params.append(software_type)

        if software_name:
            conditions.append("name=%s")
            params.append(software_name)

        if software_version:
            conditions.append("version=%s")
            params.append(software_version)

        # 构建完整的SQL语句
        sql = f"SELECT * FROM rms_software_info WHERE {' AND '.join(conditions)}"

        # 如果有参数，使用参数化查询
        if params:
            # 将%s替换为实际参数值（注意：这里为了保持与现有代码风格一致）
            formatted_params = ["'" + str(param).replace("'", "''") + "'" for param in params]
            param_index = 0
            final_sql = sql
            for condition in conditions:
                if '%s' in condition:
                    final_sql = final_sql.replace('%s', formatted_params[param_index], 1)
                    param_index += 1
        else:
            final_sql = sql

        utils.logger.debug(f"执行SQL查询: {final_sql}")

        # 执行查询
        result_list = cur.select_all(final_sql)

        utils.logger.debug(f"查询结果: {result_list}")

        # 检查查询结果
        if result_list:
            return jsonify({
                "data": result_list,
                "check_status": True,
                "total_num": len(result_list),
                "message": f"查询成功，找到 {len(result_list)} 条软件信息"
            })
        else:
            # 构建错误信息
            query_info = []
            if software_type:
                query_info.append(f"type='{software_type}'")
            if software_name:
                query_info.append(f"name='{software_name}'")
            if software_version:
                query_info.append(f"version='{software_version}'")

            if query_info:
                error_message = f"未找到匹配的软件信息: {', '.join(query_info)}"
            else:
                error_message = "rms_software_info表中无有效数据"

            return jsonify({
                "data": [],
                "check_status": False,
                "total_num": 0,
                "message": error_message
            })

    except Exception as e:
        utils.logger.error(f"软件信息查询出错: {str(e)}")
        return jsonify({
            "data": [],
            "check_status": False,
            "total_num": 0,
            "message": f"查询出错: {str(e)}"
        }), 500
